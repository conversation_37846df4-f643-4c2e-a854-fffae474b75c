'use client';

import React from 'react';
import { Bot, User, Info, Clock } from 'lucide-react';
import { ThinkingMode, hasThinkingContent, removeThinkingContent } from '../ui/ThinkingMode';
import StreamedContent from '../ui/StreamedContent';
import { ToolCallMessage } from '../tools/ToolCallMessage';
import { ChatStyle } from '../input-controls';
import { SimpleMessage } from '../../types';
import ModelLogo from '@/app/model-manager/components/ModelLogo';
import { AgentWithRelations } from '@/app/agents/types';

interface MessageItemProps {
  message: SimpleMessage;
  index: number;
  isStreaming: boolean;
  isLastMessage: boolean;
  expandedThinkingMessages: Set<string>;
  onToggleThinkingExpand: (messageId: string) => void;
  chatStyle: ChatStyle;
  selectedModel?: string;
  customModels?: Array<{
    base_model: string;
    display_name: string;
    family?: string;
  }>;
  selectedAgent?: AgentWithRelations | null;
}

export function MessageItem({
  message,
  index,
  isStreaming,
  isLastMessage,
  expandedThinkingMessages,
  onToggleThinkingExpand,
  chatStyle,
  selectedModel,
  customModels,
  selectedAgent,
}: MessageItemProps) {
  
  // 获取模型的显示信息
  const getModelDisplayInfo = (modelName?: string) => {
    if (!modelName) return { displayName: 'AI助手', family: 'default' };
    
    // 查找对应的自定义模型信息
    const customModel = customModels?.find(m => m.base_model === modelName);
    
    return {
      displayName: customModel?.display_name || modelName,
      family: customModel?.family || modelName.split(':')[0] || 'default'
    };
  };

  // 如果是工具调用占位符消息，渲染工具调用组件
  if (message.role === 'tool_call' && message.toolCall) {
    return (
      <ToolCallMessage key={message.id} toolCall={message.toolCall} />
    );
  }
  
  // 检查消息是否包含思考内容
  const hasThinking = message.role === 'assistant' && hasThinkingContent(message.content);
  const contentWithoutThinking = hasThinking ? removeThinkingContent(message.content) : message.content;
  const isCurrentlyThinking = isStreaming && message.role === 'assistant' && isLastMessage && hasThinkingContent(message.content) && !removeThinkingContent(message.content).trim();

  // 获取模型显示信息
  const modelInfo = getModelDisplayInfo(message.model || selectedModel);

  return (
    <div 
      key={message.id}
      className={`group flex gap-3 ${
        message.role === 'user' 
          ? 'flex-row-reverse' 
          : 'flex-row'
      } ${chatStyle === 'bubble' ? 'mb-4' : 'mb-6'}`}
    >
      {/* 头像 */}
      <div className={`flex-shrink-0 ${message.role === 'user' ? 'ml-2' : 'mr-2'}`}>
        {message.role === 'user' ? (
          <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
            <User className="w-5 h-5 text-white" />
          </div>
        ) : (
          <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center relative">
            {selectedAgent ? (
              <div className="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center">
                <span className="text-white text-xs font-bold">
                  {selectedAgent.name.charAt(0).toUpperCase()}
                </span>
              </div>
            ) : (
              <>
                <ModelLogo 
                  modelName={modelInfo.family} 
                  className="w-5 h-5 text-white" 
                  fallback={<Bot className="w-5 h-5 text-white" />}
                />
              </>
            )}
          </div>
        )}
      </div>

      {/* 消息内容 */}
      <div className={`flex-1 min-w-0 ${message.role === 'user' ? 'text-right' : 'text-left'}`}>
        {/* 发送者信息 */}
        <div className={`flex items-center gap-2 mb-1 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
          <span className="text-sm font-medium text-theme-foreground">
            {message.role === 'user' 
              ? '你' 
              : selectedAgent 
                ? selectedAgent.name 
                : modelInfo.displayName
            }
          </span>
          
          {/* 时间戳 */}
          <span className="text-xs text-theme-foreground-muted">
            {new Date(message.timestamp).toLocaleTimeString()}
          </span>
          
          {/* 统计信息 */}
          {message.role === 'assistant' && (message.eval_count || message.total_duration) && (
            <div className="flex items-center gap-1 text-xs text-theme-foreground-muted">
              <Clock className="w-3 h-3" />
              {message.eval_count && (
                <span>{message.eval_count} tokens</span>
              )}
              {message.total_duration && (
                <span>
                  {(message.total_duration / 1000000).toFixed(1)}s
                </span>
              )}
            </div>
          )}
        </div>

        {/* 消息气泡或内容 */}
        <div className={`${
          chatStyle === 'bubble' 
            ? `inline-block max-w-[80%] px-4 py-2 rounded-2xl ${
                message.role === 'user'
                  ? 'bg-blue-500 text-white rounded-br-md'
                  : 'bg-theme-background-secondary text-theme-foreground rounded-bl-md border border-theme-border'
              }`
            : 'w-full'
        }`}>
          {/* 思考模式内容 */}
          {hasThinking && (
            <ThinkingMode
              content={message.content}
              isExpanded={expandedThinkingMessages.has(message.id)}
              onToggleExpand={() => onToggleThinkingExpand(message.id)}
              isCurrentlyThinking={isCurrentlyThinking}
            />
          )}
          
          {/* 主要内容 */}
          {contentWithoutThinking.trim() && (
            <div className={`${hasThinking ? 'mt-2' : ''} ${
              chatStyle === 'bubble' && message.role === 'user' 
                ? 'text-white' 
                : 'text-theme-foreground'
            }`}>
              {isStreaming && isLastMessage && message.role === 'assistant' ? (
                <StreamedContent content={contentWithoutThinking} />
              ) : (
                <div 
                  className="prose prose-sm max-w-none dark:prose-invert prose-pre:bg-theme-background-secondary prose-pre:border prose-pre:border-theme-border"
                  dangerouslySetInnerHTML={{
                    __html: contentWithoutThinking
                      .replace(/\n/g, '<br>')
                      .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>')
                      .replace(/`([^`]+)`/g, '<code class="bg-theme-background-secondary px-1 py-0.5 rounded text-sm">$1</code>')
                  }}
                />
              )}
            </div>
          )}
        </div>

        {/* 错误信息显示 */}
        {message.role === 'assistant' && !contentWithoutThinking.trim() && !hasThinking && !isStreaming && (
          <div className="flex items-center gap-2 text-orange-500 text-sm mt-2">
            <Info className="w-4 h-4" />
            <span>消息内容为空</span>
          </div>
        )}
      </div>
    </div>
  );
}
