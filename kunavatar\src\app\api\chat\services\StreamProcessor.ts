import { ChatMessage, Tool, ollamaClient } from '../../../../lib/ollama';
import { StreamController } from './toolExecutionService';
import { MessageStats } from './messageStorageService';
import { StreamingChatRequest } from './streamingChatHandler';

/**
 * 流处理器 - 处理流式响应的核心逻辑
 */
export class StreamProcessor {
  /**
   * 处理消息块
   */
  static processMessageChunk(
    chunk: any,
    assistantMessage: string,
    assistantStats: MessageStats | null,
    chatRequest: StreamingChatRequest,
    streamController: StreamController
  ): { assistantMessage: string; assistantStats: MessageStats | null } {
    const { controller, encoder } = streamController;
    
    // 处理消息内容
    if (chunk.message?.content) {
      assistantMessage += chunk.message.content;
      
      // 发送内容更新
      const contentData = {
        type: 'content',
        content: chunk.message.content,
        fullContent: assistantMessage
      };
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(contentData)}\n\n`));
    }
    
    // 处理统计信息
    if (chunk.total_duration || chunk.eval_count) {
      assistantStats = {
        total_duration: chunk.total_duration,
        load_duration: chunk.load_duration,
        prompt_eval_count: chunk.prompt_eval_count,
        prompt_eval_duration: chunk.prompt_eval_duration,
        eval_count: chunk.eval_count,
        eval_duration: chunk.eval_duration,
      };
      
      // 发送统计信息更新
      const statsData = {
        type: 'stats',
        stats: assistantStats
      };
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(statsData)}\n\n`));
    }
    
    return { assistantMessage, assistantStats };
  }

  /**
   * 构建聊天请求
   */
  static buildChatRequest(chatRequest: StreamingChatRequest): any {
    const ollamaChatRequest: any = {
      model: chatRequest.model,
      messages: chatRequest.messages,
      stream: true,
      options: chatRequest.options || {}
    };

    // 添加工具支持
    if (chatRequest.enableTools && chatRequest.userSelectedTools?.length > 0) {
      ollamaChatRequest.tools = chatRequest.userSelectedTools;
    }

    return ollamaChatRequest;
  }

  /**
   * 处理流式错误
   */
  static async handleStreamError(
    error: any,
    chatRequest: StreamingChatRequest,
    assistantMessage: string,
    streamController: StreamController,
    retryWithoutTools: boolean
  ): Promise<{ shouldRetry: boolean; newRetryFlag: boolean }> {
    const { controller, encoder } = streamController;
    
    console.error('流式聊天错误:', error);
    
    // 检查是否是工具相关错误且可以重试
    const isToolError = error.message?.includes('tool') || 
                       error.message?.includes('function') ||
                       error.message?.includes('schema');
    
    if (isToolError && !retryWithoutTools && chatRequest.enableTools) {
      console.log('🔄 检测到工具错误，尝试不使用工具重新发送请求');
      
      // 发送重试通知
      const retryData = {
        type: 'retry',
        message: '工具调用失败，正在重试...'
      };
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(retryData)}\n\n`));
      
      return { shouldRetry: true, newRetryFlag: true };
    }
    
    // 发送错误信息
    const errorData = {
      type: 'error',
      error: error.message || '聊天请求失败'
    };
    controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorData)}\n\n`));
    
    return { shouldRetry: false, newRetryFlag: retryWithoutTools };
  }

  /**
   * 处理请求中断
   */
  static async handleAbort(
    conversationId: number | undefined,
    assistantMessage: string,
    model: string,
    userId: string,
    assistantStats: MessageStats | null,
    controller: ReadableStreamDefaultController
  ): Promise<void> {
    console.log('🛑 聊天请求被中断');
    
    try {
      // 如果有部分消息内容，保存它
      if (assistantMessage.trim() && conversationId) {
        const { MessageStorageService } = await import('./messageStorageService');
        await MessageStorageService.saveAssistantMessage(
          conversationId,
          assistantMessage,
          model,
          userId,
          assistantStats
        );
        console.log('💾 已保存部分生成的消息');
      }
      
      // 发送中断信号
      const abortData = {
        type: 'abort',
        message: '请求已被中断'
      };
      const encoder = new TextEncoder();
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(abortData)}\n\n`));
      
    } catch (error) {
      console.error('处理中断时出错:', error);
    } finally {
      try {
        controller.close();
      } catch (closeError) {
        console.error('关闭流控制器时出错:', closeError);
      }
    }
  }

  /**
   * 完成流处理
   */
  static async completeStream(
    chatRequest: StreamingChatRequest,
    assistantMessage: string,
    assistantStats: MessageStats | null,
    streamController: StreamController
  ): Promise<void> {
    const { controller, encoder } = streamController;
    
    try {
      // 保存助手消息
      if (assistantMessage.trim() && chatRequest.conversationId) {
        const { MessageStorageService } = await import('./messageStorageService');
        await MessageStorageService.saveAssistantMessage(
          chatRequest.conversationId,
          assistantMessage,
          chatRequest.model,
          chatRequest.userId,
          assistantStats
        );
      }
      
      // 处理标题生成
      if (chatRequest.titleSummarySettings?.enabled && 
          chatRequest.titleSummarySettings.model && 
          chatRequest.conversationId) {
        const { TitleGenerationService } = await import('./titleGenerationService');
        await TitleGenerationService.generateTitleIfNeeded(
          chatRequest.conversationId,
          chatRequest.titleSummarySettings.model,
          streamController
        );
      }
      
      // 发送完成信号
      const completeData = {
        type: 'complete',
        message: '对话完成'
      };
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(completeData)}\n\n`));
      
    } catch (error) {
      console.error('完成流处理时出错:', error);
      
      // 发送错误信息
      const errorData = {
        type: 'error',
        error: '保存消息失败'
      };
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorData)}\n\n`));
    }
  }
}
