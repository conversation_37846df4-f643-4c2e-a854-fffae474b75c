'use client';

import { useCallback, useRef } from 'react';
import { Conversation } from '@/lib/database';

interface UseChatStreamHandlersProps {
  currentConversation: Conversation | null;
  updateConversationTitle: (conversationId: number, title: string) => void;
  setMessages: (updater: (prev: any[]) => any[]) => void;
  setIsStreaming: (streaming: boolean) => void;
  setActiveTool: (tool: any) => void;
  setToolCalls: (updater: (prev: any[]) => any[]) => void;
  setCurrentAssistantMessageId: (id: string | null) => void;
  setError: (error: string | null) => void;
  loadConversations: () => Promise<void>;
}

export function useChatStreamHandlers({
  currentConversation,
  updateConversationTitle,
  setMessages,
  setIsStreaming,
  setActiveTool,
  setToolCalls,
  setCurrentAssistantMessageId,
  setError,
  loadConversations,
}: UseChatStreamHandlersProps) {
  
  const cleanupHandlersRef = useRef<Array<() => void>>([]);

  // 从数据库数据更新消息的辅助函数
  const updateMessagesFromDatabase = useCallback(async (conversationId: number) => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/conversations/${conversationId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await response.json();
      
      if (data.success && data.messages) {
        const allMessages = data.messages.sort((a: any, b: any) => {
          if (a.timestamp !== b.timestamp) {
            return a.timestamp - b.timestamp;
          }
          return a.id - b.id;
        });
        
        const formattedMessages: any[] = [];
        const toolCallMessages: any[] = [];
        
        for (const msg of allMessages) {
          if (msg.role === 'tool_call' && msg.tool_name) {
            // 处理工具调用消息
            let args = {};
            let result = '';
            
            try {
              args = msg.tool_args ? JSON.parse(msg.tool_args) : {};
            } catch (e) {
              args = {};
            }
            
            try {
              result = msg.tool_result ? 
                (typeof msg.tool_result === 'string' ? msg.tool_result : JSON.stringify(msg.tool_result)) 
                : '';
            } catch (e) {
              result = msg.tool_result || '';
            }
            
            const toolCall = {
              id: `tool-${msg.id}`,
              toolName: msg.tool_name,
              args: args,
              status: msg.tool_status || 'completed',
              result: result,
              error: msg.tool_error || undefined,
              startTime: msg.timestamp || new Date(msg.created_at).getTime(),
              executionTime: msg.tool_execution_time || 0,
            };
            
            toolCallMessages.push(toolCall);
            
            formattedMessages.push({
              id: `tool-placeholder-${msg.id}`,
              role: 'tool_call' as any,
              content: '',
              timestamp: msg.timestamp || new Date(msg.created_at).getTime(),
              toolCall: toolCall,
            });
          } else {
            formattedMessages.push({
              id: `msg-${msg.id}`,
              role: msg.role,
              content: msg.content,
              timestamp: msg.timestamp || new Date(msg.created_at).getTime(),
              model: msg.model,
              // 包含统计字段
              total_duration: msg.total_duration,
              load_duration: msg.load_duration,
              prompt_eval_count: msg.prompt_eval_count,
              prompt_eval_duration: msg.prompt_eval_duration,
              eval_count: msg.eval_count,
              eval_duration: msg.eval_duration,
            });
          }
        }
        
        setMessages(() => formattedMessages);
        setToolCalls(() => toolCallMessages);
      }
    } catch (err) {
      console.error('获取统计信息失败:', err);
    }
  }, [setMessages, setToolCalls]);

  // 创建流处理器句柄
  const createStreamHandlers = useCallback(() => {
    const handlers = {
      onMessageUpdate: (messageId: string, content: string, stats?: any) => {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === messageId 
              ? { ...msg, content, ...(stats || {}) }
              : msg
          )
        );
      },
      onToolCallStart: (toolCall: any) => {
        setActiveTool(toolCall);
        setToolCalls(prev => [...prev, toolCall]);
        
        const toolCallMessage = {
          id: `tool-runtime-${toolCall.id}`,
          role: 'tool_call' as const,
          content: '',
          timestamp: Date.now(),
          toolCall: toolCall,
        };
        setMessages(prev => [...prev, toolCallMessage]);
      },
      onToolCallComplete: (toolCallId: string, toolName: string, result: string, executionTime?: number) => {
        setActiveTool(null);
        
        setToolCalls(prev => 
          prev.map(tc => {
            const isMatch = toolCallId 
              ? tc.id === toolCallId
              : tc.toolName === toolName && tc.status === 'executing';
            
            return isMatch
              ? { 
                  ...tc, 
                  status: 'completed' as const,
                  result: typeof result === 'string' ? result : JSON.stringify(result),
                  executionTime: executionTime || (Date.now() - tc.startTime)
                }
              : tc;
          })
        );
        
        setMessages(prev => 
          prev.map(msg => {
            if (msg.role === 'tool_call' && msg.toolCall) {
              const isMatch = toolCallId 
                ? msg.toolCall.id === toolCallId
                : msg.toolCall.toolName === toolName && msg.toolCall.status === 'executing';
              
              if (isMatch) {
                return {
                  ...msg,
                  toolCall: {
                    id: msg.toolCall.id,
                    toolName: msg.toolCall.toolName,
                    args: msg.toolCall.args,
                    status: 'completed' as const,
                    result: typeof result === 'string' ? result : JSON.stringify(result),
                    startTime: msg.toolCall.startTime,
                    executionTime: executionTime || (Date.now() - msg.toolCall.startTime)
                  }
                };
              }
            }
            return msg;
          })
        );
      },
      onToolCallError: (toolCallId: string, toolName: string, error: string, executionTime?: number) => {
        setActiveTool(null);
        
        setToolCalls(prev => 
          prev.map(tc => {
            const isMatch = toolCallId 
              ? tc.id === toolCallId
              : tc.toolName === toolName && tc.status === 'executing';
            
            return isMatch
              ? { 
                  ...tc, 
                  status: 'error' as const,
                  error: error || '工具调用失败',
                  executionTime: executionTime || (Date.now() - tc.startTime)
                }
              : tc;
          })
        );
        
        setMessages(prev => 
          prev.map(msg => {
            if (msg.role === 'tool_call' && msg.toolCall) {
              const isMatch = toolCallId 
                ? msg.toolCall.id === toolCallId
                : msg.toolCall.toolName === toolName && msg.toolCall.status === 'executing';
              
              if (isMatch) {
                return {
                  ...msg,
                  toolCall: {
                    id: msg.toolCall.id,
                    toolName: msg.toolCall.toolName,
                    args: msg.toolCall.args,
                    status: 'error' as const,
                    error: error || '工具调用失败',
                    startTime: msg.toolCall.startTime,
                    executionTime: executionTime || (Date.now() - msg.toolCall.startTime)
                  }
                };
              }
            }
            return msg;
          })
        );
      },
      onNewAssistantMessage: (messageId: string, selectedModel: string) => {
        const newAssistantMessage = {
          id: messageId,
          role: 'assistant' as const,
          content: '',
          timestamp: Date.now(),
          model: selectedModel,
        };
        
        setMessages(prev => [...prev, newAssistantMessage]);
        setCurrentAssistantMessageId(messageId);
      },
      onTitleUpdate: (conversationId: number, title: string) => {
        console.log('📝 处理标题更新事件:', conversationId, title);
        updateConversationTitle(conversationId, title);
      },
      onStreamEnd: () => {
        setIsStreaming(false);
        setActiveTool(null);
        setCurrentAssistantMessageId(null);
        
        // 优化：更智能的统计信息获取策略，减少API调用
        const cleanup = () => {
          if (currentConversation) {
            console.log('🔧 对话完成，准备获取统计信息');
            
            // 使用单次调用获取统计信息，如果没有则等待后重试一次
            const fetchStats = async (retryOnce = true) => {
              try {
                const hasStats = await updateMessagesFromDatabase(currentConversation.id);
                
                if (!hasStats && retryOnce) {
                  console.log('⏳ 统计信息未就绪，1秒后重试一次');
                  setTimeout(() => fetchStats(false), 1000);
                }
              } catch (err) {
                console.error('获取统计信息失败:', err);
              }
            };
            
            // 延迟300ms后开始获取，给服务器时间保存统计信息
            setTimeout(() => fetchStats(), 300);
          } else {
            console.log('🔄 无当前对话，刷新对话列表');
            loadConversations();
          }
        };
        
        // 添加到清理队列
        cleanupHandlersRef.current.push(cleanup);
        cleanup();
        
        // 从清理队列中移除
        setTimeout(() => {
          const index = cleanupHandlersRef.current.indexOf(cleanup);
          if (index > -1) {
            cleanupHandlersRef.current.splice(index, 1);
          }
        }, 3000);
      },
      onError: (errorMessage: string) => {
        setError(errorMessage);
        setIsStreaming(false);
      },
    };
    return handlers;
  }, [
    updateMessagesFromDatabase, 
    currentConversation, 
    updateConversationTitle,
    setMessages,
    setActiveTool,
    setToolCalls,
    setCurrentAssistantMessageId,
    setIsStreaming,
    setError,
    loadConversations
  ]);

  return {
    createStreamHandlers,
    updateMessagesFromDatabase,
  };
}
