'use client';

import { useCallback } from 'react';
import { CustomModel } from '@/lib/database/custom-models';
import { Conversation } from '@/lib/database';

interface UseChatPageLogicProps {
  models: CustomModel[];
  selectedModel: string;
  currentConversation: Conversation | null;
  conversationLoading: boolean;
  createConversation: (title: string, model: string) => Promise<number | null>;
  switchConversation: (conversationId: number) => Promise<void>;
  setSelectedModel: (model: string, conversationId?: number) => void;
  setMessages: (messages: any[]) => void;
  setToolCalls: (toolCalls: any[]) => void;
  selectBestModel: (
    availableModels: CustomModel[],
    conversationId?: number,
    lastUsedModel?: string,
    conversationModel?: string
  ) => void;
}

export function useChatPageLogic({
  models,
  selectedModel,
  currentConversation,
  conversationLoading,
  createConversation,
  switchConversation,
  setSelectedModel,
  setMessages,
  setToolCalls,
  selectBestModel,
}: UseChatPageLogicProps) {
  
  // 从数据库数据更新消息的辅助函数
  const updateMessagesFromDatabase = useCallback((dbMessages: any[]) => {
    console.log('🔧 更新消息数据，总数:', dbMessages.length);
    
    // 使用与useMessageLoader相同的逻辑处理工具调用消息
    const allMessages = dbMessages.sort((a: any, b: any) => {
      if (a.timestamp !== b.timestamp) {
        return a.timestamp - b.timestamp;
      }
      return a.id - b.id;
    });
    
    const formattedMessages: any[] = [];
    const toolCallMessages: any[] = [];
    
    for (const msg of allMessages) {
      if (msg.role === 'tool_call' && msg.tool_name) {
        // 处理工具调用消息
        let args = {};
        let result = '';
        
        try {
          args = msg.tool_args ? JSON.parse(msg.tool_args) : {};
        } catch (e) {
          args = {};
        }
        
        try {
          result = msg.tool_result ? 
            (typeof msg.tool_result === 'string' ? msg.tool_result : JSON.stringify(msg.tool_result)) 
            : '';
        } catch (e) {
          result = msg.tool_result || '';
        }
        
        const toolCall = {
          id: `tool-${msg.id}`,
          toolName: msg.tool_name,
          args: args,
          status: msg.tool_status || 'completed',
          result: result,
          error: msg.tool_error || undefined,
          startTime: msg.timestamp || new Date(msg.created_at).getTime(),
          executionTime: msg.tool_execution_time || 0,
        };
        
        toolCallMessages.push(toolCall);
        
        formattedMessages.push({
          id: `tool-placeholder-${msg.id}`,
          role: 'tool_call' as any,
          content: '',
          timestamp: msg.timestamp || new Date(msg.created_at).getTime(),
          toolCall: toolCall,
        });
      } else {
        formattedMessages.push({
          id: `msg-${msg.id}`,
          role: msg.role,
          content: msg.content,
          timestamp: msg.timestamp || new Date(msg.created_at).getTime(),
          model: msg.model,
          // 包含统计字段
          total_duration: msg.total_duration,
          load_duration: msg.load_duration,
          prompt_eval_count: msg.prompt_eval_count,
          prompt_eval_duration: msg.prompt_eval_duration,
          eval_count: msg.eval_count,
          eval_duration: msg.eval_duration,
        });
      }
    }
    
    // 检查是否有统计信息
    const hasStats = formattedMessages.some((msg: any) => 
      msg.role === 'assistant' && (msg.total_duration || msg.eval_count)
    );
    console.log('🔧 更新后的消息是否包含统计信息:', hasStats);
    console.log('🔧 更新后的工具调用数量:', toolCallMessages.length);
    
    setMessages(formattedMessages);
    setToolCalls(toolCallMessages);
  }, [setMessages, setToolCalls]);

  // 清空当前对话
  const clearCurrentChat = useCallback(async () => {
    if (!currentConversation) return;
    
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/conversations/${currentConversation.id}/clear`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      if (response.ok) {
        // 清空当前消息
        setMessages([]);
        setToolCalls([]);
        
        // 重新加载对话列表 - 这里需要从外部传入
        // loadConversations();
      }
    } catch (error) {
      console.error('清空对话失败:', error);
      throw error;
    }
  }, [currentConversation, setMessages, setToolCalls]);

  // 插入文本到输入框
  const handleInsertText = useCallback((text: string, setInputMessage: (text: string) => void) => {
    setInputMessage(text);
  }, []);

  return {
    updateMessagesFromDatabase,
    clearCurrentChat,
    handleInsertText,
  };
}
