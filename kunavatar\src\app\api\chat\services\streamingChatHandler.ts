import { ChatMess<PERSON>, Tool, ollamaClient } from '../../../../lib/ollama';
import { mcpServerClient } from '../../../../lib/mcp/mcp-client-server';
import { TitleGenerationService, TitleSummarySettings } from './titleGenerationService';
import { ToolExecutionService, StreamController } from './toolExecutionService';
import { MessageStorageService, MessageStats } from './messageStorageService';
import { ValidationService } from './validationService';
import { MemoryService } from './memoryService';
import { StreamProcessor } from './StreamProcessor';
import { ToolCallProcessor } from './ToolCallProcessor';
import { MemoryContextInjector } from './MemoryContextInjector';

export interface StreamingChatRequest {
  model: string;
  messages: ChatMessage[];
  conversationId?: number;
  agentId?: number;
  userId: string;
  options?: any;
  enableTools?: boolean;
  selectedTools?: string[];
  titleSummarySettings?: TitleSummarySettings;
  userSelectedTools: Tool[];
}

/**
 * 流式聊天处理服务
 */
export class StreamingChatHandler {
  /**
   * 处理流式聊天请求
   */
  static async handleStreamingChat(
    request: Request,
    chatRequest: StreamingChatRequest
  ): Promise<Response> {
    const encoder = new TextEncoder();
    
    const readableStream = new ReadableStream({
      async start(controller) {
        let assistantMessage = '';
        let assistantStats: MessageStats | null = null;
        
        // 创建流控制器
        const streamController: StreamController = { controller, encoder };
        
                 // 监听请求中断信号
         const abortHandler = () => StreamProcessor.handleAbort(
           chatRequest.conversationId,
           assistantMessage,
           chatRequest.model,
           chatRequest.userId,
           assistantStats,
           controller
         );
         
         request.signal?.addEventListener('abort', abortHandler);
         
         try {
           // 注入记忆上下文
           const messagesWithMemory = await MemoryContextInjector.injectMemoryContext(
             chatRequest.messages,
             chatRequest.conversationId,
             chatRequest.userId
           );

           // 构建聊天请求
           const ollamaChatRequest = StreamProcessor.buildChatRequest({
             ...chatRequest,
             messages: messagesWithMemory
           });

           console.log('发送聊天请求:', JSON.stringify(ollamaChatRequest, null, 2));

           let retryWithoutTools = false;

           try {
             // 立即保存用户消息，确保时间顺序正确
             StreamingChatHandler.saveUserMessageIfExists(chatRequest);

             // 使用流式API
             for await (const chunk of ollamaClient.chatStream(ollamaChatRequest)) {
               // 处理工具调用
               if (chunk.message?.tool_calls && chunk.message.tool_calls.length > 0) {
                 await ToolCallProcessor.handleToolCallsInStream(
                   chunk.message.tool_calls,
                   chatRequest,
                   assistantMessage,
                   streamController
                 );
                 // 重置助手消息，为工具调用后的回复做准备
                 assistantMessage = '';
               } else {
                 // 处理普通消息块
                 const result = StreamProcessor.processMessageChunk(
                   chunk,
                   assistantMessage,
                   assistantStats,
                   chatRequest,
                   streamController
                 );
                 assistantMessage = result.assistantMessage;
                 assistantStats = result.assistantStats;
               }
             }

             // 完成流处理
             await StreamProcessor.completeStream(
               chatRequest,
               assistantMessage,
               assistantStats,
               streamController
             );

             // 发送结束标志
             controller.enqueue(encoder.encode('data: [DONE]\n\n'));
             controller.close();

           } catch (streamError) {
             // 处理流式错误并可能重试
             const errorResult = await StreamProcessor.handleStreamError(
               streamError,
               chatRequest,
               retryWithoutTools,
               assistantMessage,
               assistantStats,
               streamController
             );
           }
         } catch (error) {
           StreamingChatHandler.handleFatalError(error, controller, encoder);
         } finally {
           // 清理事件监听器
           request.signal?.removeEventListener('abort', abortHandler);
         }
      }
    });

    return new Response(readableStream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });
  }



  /**
   * 保存用户消息（如果存在）
   */
  private static saveUserMessageIfExists(chatRequest: StreamingChatRequest): void {
    if (!chatRequest.conversationId) return;

    const lastUserMessage = MessageStorageService.extractLastUserMessage(chatRequest.messages);
    if (lastUserMessage) {
      MessageStorageService.saveUserMessage(
        chatRequest.conversationId,
        lastUserMessage.content,
        chatRequest.model,
        chatRequest.userId
      );
    }
  }

  /**
   * 处理工具调用
   */
  private static async handleToolCallsInStream(
    toolCalls: any[],
    chatRequest: StreamingChatRequest,
    assistantMessage: string,
    streamController: StreamController
  ): Promise<void> {
    // 先保存AI决定调用工具前的回复内容（如果有）
    if (chatRequest.conversationId && assistantMessage.trim()) {
      try {
        MessageStorageService.saveAssistantMessage(
          chatRequest.conversationId,
          assistantMessage,
          chatRequest.model,
          chatRequest.userId
        );
      } catch (dbError) {
        console.error('保存AI工具调用前回复失败:', dbError);
      }
    }

    // 执行工具调用并获取结果
    const toolResults = await ToolExecutionService.handleToolCallsStream(
      toolCalls,
      chatRequest.userSelectedTools,
      chatRequest.conversationId,
      chatRequest.model,
      chatRequest.userId,
      streamController
    );

    // 继续对话以获取基于工具结果的回复
    await StreamingChatHandler.continueConversationAfterTools(toolCalls, toolResults, chatRequest, streamController);
  }

  /**
   * 工具调用后继续对话
   */
  private static async continueConversationAfterTools(
    toolCalls: any[],
    toolResults: any[],
    chatRequest: StreamingChatRequest,
    streamController: StreamController
  ): Promise<void> {
    // 构建包含工具结果的消息历史
    const updatedMessages: ChatMessage[] = [
      ...chatRequest.messages,
      {
        role: 'assistant' as const,
        content: '',
        tool_calls: toolCalls
      }
    ];

    // 添加实际的工具结果消息
    for (const toolResult of toolResults) {
      if (toolResult.result) {
        updatedMessages.push({
          role: 'tool' as const,
          content: toolResult.result
        });
      } else if (toolResult.error) {
        updatedMessages.push({
          role: 'tool' as const,
          content: `工具执行错误: ${toolResult.error}`
        });
      }
    }

    console.log('🔧 构建的消息历史包含工具结果:', JSON.stringify(updatedMessages.slice(-3), null, 2));

    // 为工具调用后的对话也注入记忆上下文
    let messagesWithMemory = updatedMessages;
    if (chatRequest.agentId && chatRequest.conversationId) {
      const memoryContext = MemoryService.getMemoryContext(chatRequest.conversationId);
      
      if (memoryContext.trim()) {
        messagesWithMemory = StreamingChatHandler.injectMemoryContext(messagesWithMemory, memoryContext);
      }
    }

    // 继续对话以获取基于工具结果的回复
    const followUpRequest = {
      model: chatRequest.model,
      messages: messagesWithMemory,
      stream: true,
      options: chatRequest.options
    };

    let followUpMessage = '';
    for await (const followUpChunk of ollamaClient.chatStream(followUpRequest)) {
      if (followUpChunk.message?.content) {
        followUpMessage += followUpChunk.message.content;
      }

      const followUpData = `data: ${JSON.stringify(followUpChunk)}\n\n`;
      streamController.controller.enqueue(streamController.encoder.encode(followUpData));

      if (followUpChunk.done && chatRequest.conversationId && followUpMessage.trim()) {
        // 立即保存工具调用后的助手回复
        MessageStorageService.saveAssistantMessage(
          chatRequest.conversationId,
          followUpMessage,
          chatRequest.model,
          chatRequest.userId,
          MessageStorageService.extractStatsFromChunk(followUpChunk) || undefined
        );

        // 检查是否需要触发记忆生成
        StreamingChatHandler.checkAndGenerateMemory(
          chatRequest,
          followUpMessage
        );

        // 检查是否需要生成标题
        StreamingChatHandler.checkAndGenerateTitle(
          chatRequest.conversationId,
          chatRequest.titleSummarySettings,
          streamController
        );
        break;
      }
    }
  }

  /**
   * 处理消息块
   */
  private static processMessageChunk(
    chunk: any,
    assistantMessage: string,
    assistantStats: MessageStats | null,
    chatRequest: StreamingChatRequest,
    streamController: StreamController
  ): { assistantMessage: string; assistantStats: MessageStats | null } {
    // 累积助手的回复内容
    if (chunk.message?.content) {
      assistantMessage += chunk.message.content;
    }

    // 更新统计信息
    const newStats = MessageStorageService.extractStatsFromChunk(chunk);
    if (newStats) {
      assistantStats = newStats;
      console.log('🔧 收到统计信息:', assistantStats);
    }

    // 发送数据块到客户端
    const data = `data: ${JSON.stringify(chunk)}\n\n`;
    streamController.controller.enqueue(streamController.encoder.encode(data));

    // 如果完成，立即保存助手回复
    if (chunk.done && chatRequest.conversationId && assistantMessage.trim()) {
      try {
        const statsToSave = MessageStorageService.extractStatsFromChunk(chunk) || assistantStats;
        console.log('🔧 保存助手消息，统计信息:', statsToSave);

        MessageStorageService.saveAssistantMessage(
          chatRequest.conversationId,
          assistantMessage,
          chatRequest.model,
          chatRequest.userId,
          statsToSave || undefined
        );

        // 检查是否需要生成记忆
        StreamingChatHandler.checkAndGenerateMemory(chatRequest, assistantMessage);

        // 检查是否需要生成标题
        StreamingChatHandler.checkAndGenerateTitle(
          chatRequest.conversationId,
          chatRequest.titleSummarySettings,
          streamController
        );
      } catch (dbError) {
        console.error('保存助手消息到数据库失败:', dbError);
      }
    }

    return { assistantMessage, assistantStats };
  }

  /**
   * 检查并生成记忆
   */
  private static checkAndGenerateMemory(
    chatRequest: StreamingChatRequest,
    assistantMessage: string
  ): void {
    const { conversationId, agentId, messages } = chatRequest;
    
    if (!conversationId || !agentId) return;
    
    if (MemoryService.shouldTriggerMemory(conversationId, agentId)) {
      // 异步生成记忆，不阻塞响应
      (async () => {
        try {
          console.log(`🧠 触发记忆生成 - 对话: ${conversationId}, 智能体: ${agentId}`);
          const memorySettings = MemoryService.getGlobalMemorySettings();
          const conversationMessages = [...messages];
          if (assistantMessage.trim()) {
            conversationMessages.push({
              role: 'assistant',
              content: assistantMessage
            });
          }
          
          await MemoryService.generateMemory({
            conversationId,
            agentId,
            messages: conversationMessages,
            settings: memorySettings
          });
        } catch (memoryError) {
          console.error('记忆生成失败:', memoryError);
        }
      })();
    }
  }

  /**
   * 检查并生成标题
   */
  private static checkAndGenerateTitle(
    conversationId: number,
    titleSummarySettings: TitleSummarySettings | undefined,
    streamController: StreamController
  ): void {
    TitleGenerationService.checkAndGenerateTitle(conversationId, titleSummarySettings)
      .then(newTitle => {
        if (newTitle) {
          TitleGenerationService.sendTitleUpdateEvent(
            streamController.controller,
            streamController.encoder,
            conversationId,
            newTitle
          );
        }
      })
      .catch(error => {
        console.error('生成标题时出错:', error);
      });
  }

  /**
   * 处理流式错误
   */
  private static async handleStreamError(
    streamError: any,
    chatRequest: StreamingChatRequest,
    retryWithoutTools: boolean,
    assistantMessage: string,
    assistantStats: MessageStats | null,
    streamController: StreamController
  ): Promise<void> {
    console.error('流式请求错误:', streamError);

    const isToolsNotSupported = ValidationService.isToolsNotSupportedError(streamError);

    // 如果启用了工具且出现工具不支持错误，尝试不使用工具重新请求
    if (chatRequest.enableTools && !retryWithoutTools && isToolsNotSupported) {
      console.log('模型不支持工具调用，尝试不使用工具重新请求');
      await StreamingChatHandler.retryWithoutTools(chatRequest, assistantMessage, assistantStats, streamController);
    } else {
      // 如果已经重试过或者没有启用工具，或者不是工具不支持的错误，直接抛出错误
      throw streamError;
    }
  }

  /**
   * 不使用工具重试请求
   */
  private static async retryWithoutTools(
    chatRequest: StreamingChatRequest,
    assistantMessage: string,
    assistantStats: MessageStats | null,
    streamController: StreamController
  ): Promise<void> {
    // 重置助手消息内容，避免重复累积
    assistantMessage = '';

    // 重新构建不包含工具的请求
    const retryRequest = {
      model: chatRequest.model,
      messages: chatRequest.messages,
      stream: true,
      options: chatRequest.options
    };

    // 重新尝试流式API
    for await (const chunk of ollamaClient.chatStream(retryRequest)) {
      const result = StreamingChatHandler.processMessageChunk(
        chunk,
        assistantMessage,
        assistantStats,
        chatRequest,
        streamController
      );
      assistantMessage = result.assistantMessage;
      assistantStats = result.assistantStats;
    }

    // 发送结束标志
    streamController.controller.enqueue(streamController.encoder.encode('data: [DONE]\n\n'));
    streamController.controller.close();
  }

  /**
   * 处理请求中断
   */
  private static handleAbort(
    conversationId: number | undefined,
    assistantMessage: string,
    model: string,
    userId: string,
    assistantStats: MessageStats | null,
    controller: ReadableStreamDefaultController<Uint8Array>
  ): void {
    console.log('🛑 检测到请求中断，保存已生成的内容');

    if (conversationId) {
      MessageStorageService.saveAbortedAssistantMessage(
        conversationId,
        assistantMessage,
        model,
        userId,
        assistantStats || undefined
      );
    }

    // 关闭控制器
    try {
      controller.close();
    } catch (e) {
      // 忽略重复关闭的错误
    }
  }

  /**
   * 处理致命错误
   */
  private static handleFatalError(
    error: any,
    controller: ReadableStreamDefaultController<Uint8Array>,
    encoder: TextEncoder
  ): void {
    console.error('流式聊天失败:', error);
    const errorData = `data: ${JSON.stringify({
      error: true,
      message: error instanceof Error ? error.message : '聊天请求失败'
    })}\n\n`;
    controller.enqueue(encoder.encode(errorData));
    controller.close();
  }
}